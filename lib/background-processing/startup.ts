import { startBackgroundWorker } from './worker';

let isInitialized = false;

export function initializeBackgroundProcessing() {
  if (isInitialized) return;

  const processId = process.pid;
  console.log(`[Startup:PID-${processId}] Initializing background processing...`);

  // Start the background worker
  startBackgroundWorker();

  isInitialized = true;
  console.log(`[Startup:PID-${processId}] Background processing initialized`);
}

// Auto-initialize when imported
if (typeof window === 'undefined') {
  // Only run on server side
  const processId = process.pid;
  console.log(`[Startup:PID-${processId}] Auto-initializing from import...`);
  initializeBackgroundProcessing();
}