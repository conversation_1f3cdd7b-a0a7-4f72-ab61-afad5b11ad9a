-- Fix worker RLS issue by adding SECURITY DEFINER to worker functions
-- This migration fixes the issue where workers were falling back to the old method
-- because the claim_pending_conversation function couldn't bypass RLS policies

-- Fix the claim_pending_conversation function to use SECURITY DEFINER
CREATE OR REPLACE FUNCTION claim_pending_conversation(worker_id TEXT)
RETURNS TABLE(
  id UUID,
  user_id UUID,
  youtube_video_id VARCHAR(11)
) 
LANGUAGE plpgsql
SECURITY DEFINER  -- This allows the function to bypass RLS
AS $$
DECLARE
  claimed_conversation RECORD;
BEGIN
  -- Use FOR UPDATE SKIP LOCKED to atomically claim a conversation
  -- This prevents race conditions by locking the row before updating
  SELECT c.id, c.user_id, c.youtube_video_id
  INTO claimed_conversation
  FROM conversations c
  WHERE c.processing_status = 'pending'
  ORDER BY c.created_at ASC
  FOR UPDATE SKIP LOCKED
  LIMIT 1;
  
  -- If we found a conversation, claim it
  IF FOUND THEN
    -- Update the conversation to mark it as being processed by this worker
    UPDATE conversations 
    SET 
      processing_status = 'processing',
      processing_started_at = NOW(),
      worker_id = claim_pending_conversation.worker_id
    WHERE conversations.id = claimed_conversation.id;
    
    -- Return the claimed conversation
    RETURN QUERY SELECT 
      claimed_conversation.id,
      claimed_conversation.user_id,
      claimed_conversation.youtube_video_id;
  END IF;
  
  -- If no conversation was found/claimed, return empty result
  RETURN;
END;
$$;

-- Fix the release_conversation function
CREATE OR REPLACE FUNCTION release_conversation(conversation_id UUID, worker_id TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER  -- This allows the function to bypass RLS
AS $$
BEGIN
  -- Only allow the worker that claimed the conversation to release it
  UPDATE conversations 
  SET 
    processing_status = 'pending',
    processing_started_at = NULL,
    worker_id = NULL
  WHERE 
    id = conversation_id 
    AND conversations.worker_id = release_conversation.worker_id
    AND processing_status = 'processing';
  
  -- Return true if a row was updated (conversation was released)
  RETURN FOUND;
END;
$$;

-- Fix the cleanup_stale_conversations function
CREATE OR REPLACE FUNCTION cleanup_stale_conversations(timeout_minutes INTEGER DEFAULT 30)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER  -- This allows the function to bypass RLS
AS $$
DECLARE
  cleaned_count INTEGER;
BEGIN
  -- Reset conversations that have been processing for too long
  UPDATE conversations 
  SET 
    processing_status = 'pending',
    processing_started_at = NULL,
    worker_id = NULL,
    processing_error = 'Processing timed out - worker may have crashed'
  WHERE 
    processing_status = 'processing'
    AND processing_started_at < NOW() - INTERVAL '1 minute' * timeout_minutes;
  
  GET DIAGNOSTICS cleaned_count = ROW_COUNT;
  
  RETURN cleaned_count;
END;
$$;

-- Show completion message
SELECT 'Worker RLS fix migration completed successfully!' AS status;
