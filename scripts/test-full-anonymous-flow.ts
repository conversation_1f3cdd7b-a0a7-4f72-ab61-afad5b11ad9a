#!/usr/bin/env tsx

/**
 * Test script to verify the full anonymous user flow works correctly
 * This simulates creating a videotext with a real YouTube URL
 */

import { v4 as uuidv4 } from 'uuid';

const BASE_URL = 'http://localhost:3002';
const TEST_SESSION_ID = uuidv4();
const TEST_YOUTUBE_URL = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'; // Rick <PERSON> for testing

async function createAnonymousConversation() {
  console.log('🧪 Creating anonymous conversation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/product/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': TEST_SESSION_ID
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('   ✅ Conversation created:', result.conversation_id);
      return result.conversation_id;
    } else {
      console.log('   ❌ Failed to create conversation:', result);
      return null;
    }
  } catch (error) {
    console.error('   ❌ Error creating conversation:', error);
    return null;
  }
}

async function startProcessing(conversationId: string) {
  console.log('🧪 Starting YouTube processing...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/product/conversations/${conversationId}/start-processing`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': TEST_SESSION_ID
      },
      body: JSON.stringify({ youtubeUrl: TEST_YOUTUBE_URL })
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('   ✅ Processing started successfully');
      return true;
    } else {
      console.log('   ❌ Failed to start processing:', result);
      return false;
    }
  } catch (error) {
    console.error('   ❌ Error starting processing:', error);
    return false;
  }
}

async function monitorProgress(conversationId: string, maxChecks: number = 5) {
  console.log('🧪 Monitoring progress...');
  
  for (let i = 0; i < maxChecks; i++) {
    try {
      const response = await fetch(`${BASE_URL}/api/product/conversations/${conversationId}/status`, {
        headers: {
          'x-session-id': TEST_SESSION_ID
        }
      });
      
      const result = await response.json();
      
      if (response.ok) {
        console.log(`   📊 Check ${i + 1}: Status = ${result.status}, Title = "${result.title}"`);
        
        if (result.status === 'completed' || result.status === 'failed') {
          console.log('   ✅ Processing completed');
          return true;
        }
      } else {
        console.log(`   ❌ Status check ${i + 1} failed:`, result);
        return false;
      }
      
      // Wait 2 seconds between checks
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`   ❌ Error in status check ${i + 1}:`, error);
      return false;
    }
  }
  
  console.log('   ⏰ Monitoring completed (may still be processing)');
  return true;
}

async function testProgressEndpoint(conversationId: string) {
  console.log('🧪 Testing progress endpoint access...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/product/conversations/${conversationId}/progress`, {
      headers: {
        'x-session-id': TEST_SESSION_ID
      }
    });
    
    if (response.status === 200) {
      console.log('   ✅ Progress endpoint accessible (no 401 error)');
      return true;
    } else {
      console.log('   ❌ Progress endpoint returned:', response.status);
      return false;
    }
  } catch (error) {
    console.error('   ❌ Error accessing progress endpoint:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting full anonymous flow test...');
  console.log(`   Session ID: ${TEST_SESSION_ID}`);
  console.log(`   YouTube URL: ${TEST_YOUTUBE_URL}`);
  
  try {
    // Step 1: Create conversation
    const conversationId = await createAnonymousConversation();
    if (!conversationId) {
      console.log('❌ Cannot continue without conversation');
      return;
    }
    
    // Step 2: Test progress endpoint before processing starts
    await testProgressEndpoint(conversationId);
    
    // Step 3: Start processing
    const processingStarted = await startProcessing(conversationId);
    if (!processingStarted) {
      console.log('❌ Cannot continue without starting processing');
      return;
    }
    
    // Step 4: Monitor progress
    await monitorProgress(conversationId);
    
    console.log('\n✅ Full anonymous flow test completed successfully!');
    console.log('   No 401 errors should have occurred during this test.');
    
  } catch (error) {
    console.error('❌ Full flow test failed:', error);
  }
}

if (require.main === module) {
  main();
}
