import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, matchesConversationOwnership } from '@/lib/auth/apiAuth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  try {
    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Get conversation and verify ownership using admin client
    const { data: conversation, error: convError } = await supabaseAdmin
      .from('conversations')
      .select('id, processing_status, user_id, session_id, is_anonymous')
      .eq('id', conversationId)
      .single();

    if (convError || !conversation) {
      return new Response('Conversation not found', { status: 404 });
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      return new Response('Access denied', { status: 403 });
    }

    // Create SSE stream
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        let intervalId: NodeJS.Timeout;
        let isActive = true;
        let isClosed = false;

        const safeEnqueue = (data: string) => {
          if (!isClosed && isActive) {
            try {
              controller.enqueue(encoder.encode(data));
            } catch (error) {
              console.error('Error enqueueing data:', error);
              isClosed = true;
              isActive = false;
            }
          }
        };

        const safeClose = () => {
          if (!isClosed && isActive) {
            try {
              controller.close();
              isClosed = true;
              isActive = false;
            } catch (error) {
              console.error('Error closing controller:', error);
              isClosed = true;
              isActive = false;
            }
          }
        };

        const cleanup = () => {
          if (intervalId) {
            clearInterval(intervalId);
          }
          safeClose();
        };

        const sendUpdate = async () => {
          if (!isActive || isClosed) return;

          try {
            const { data: currentConv } = await supabaseAdmin
              .from('conversations')
              .select('processing_status, processing_progress, current_step_text, processing_error, title')
              .eq('id', conversationId)
              .single();

            if (currentConv && isActive && !isClosed) {
              const progress = currentConv.processing_progress || {};
              const progressData = {
                conversationId,
                status: currentConv.processing_status,
                title: currentConv.title, // Include conversation title for real-time updates
                progress: {
                  currentStep: progress.currentStep || currentConv.processing_status,
                  transcriptProgress: progress.transcriptProgress || 0,
                  optimizationProgress: progress.optimizationProgress || 0,
                  summaryProgress: progress.summaryProgress || 0,
                  transcriptLength: progress.transcriptLength || 0,
                  currentStepText: currentConv.current_step_text || progress.currentStepText || 'Processing...',
                  partialSummary: progress.partialSummary || null // Include streaming summary content
                },
                error: currentConv.processing_error,
                timestamp: new Date().toISOString()
              };

              try {
                const jsonData = JSON.stringify(progressData);
                safeEnqueue(`data: ${jsonData}\n\n`);
              } catch (jsonError) {
                console.error('Error serializing progress data:', jsonError, progressData);
                // Send a safe fallback message
                const fallbackData = {
                  conversationId,
                  status: currentConv.processing_status,
                  error: 'Error formatting progress data',
                  timestamp: new Date().toISOString()
                };
                safeEnqueue(`data: ${JSON.stringify(fallbackData)}\n\n`);
              }

              // Close stream if processing is complete or failed
              if (currentConv.processing_status === 'completed' || currentConv.processing_status === 'failed') {
                safeEnqueue('data: [DONE]\n\n');
                cleanup();
              }
            }
          } catch (error) {
            console.error('Error sending progress update:', error);
            if (isActive && !isClosed) {
              try {
                const errorData = {
                  error: 'Failed to get progress',
                  timestamp: new Date().toISOString()
                };
                safeEnqueue(`data: ${JSON.stringify(errorData)}\n\n`);
              } catch (jsonError) {
                console.error('Error serializing error data:', jsonError);
                // Send a minimal safe message
                safeEnqueue(`data: {"error":"Processing error"}\n\n`);
              }
            }
          }
        };

        // Send initial update
        sendUpdate();

        // Send updates every 1 second
        intervalId = setInterval(sendUpdate, 1000);

        // Cleanup on close
        request.signal?.addEventListener('abort', () => {
          cleanup();
        });

        // Timeout after 10 minutes
        setTimeout(() => {
          if (isActive && !isClosed) {
            safeEnqueue('data: [TIMEOUT]\n\n');
            cleanup();
          }
        }, 10 * 60 * 1000); // 10 minutes
      },

      cancel() {
        // Stream was cancelled - cleanup will be handled by abort event
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });

  } catch (error) {
    console.error('Error setting up progress stream:', error);
    return new Response('Internal server error', { status: 500 });
  }
} 