import { ConversationProcessor } from './conversationProcessor';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { cancellationManager } from './cancellationManager';

// Worker metrics tracking
interface WorkerMetrics {
  startTime: Date;
  conversationsProcessed: number;
  conversationsFailed: number;
  lastActivity: Date;
  currentConversationId: string | null;
  currentConversationStartTime: Date | null;
  errors: Array<{ timestamp: Date; error: string; conversationId?: string }>;
}

// Enhanced logging utility
class WorkerLogger {
  static formatTimestamp(): string {
    return new Date().toISOString();
  }

  static log(workerId: string, level: 'INFO' | 'WARN' | 'ERROR', message: string, meta?: any) {
    const timestamp = this.formatTimestamp();
    const logMessage = `[${timestamp}] [${level}] [Worker:${workerId}] ${message}`;

    if (level === 'ERROR') {
      console.error(logMessage, meta || '');
    } else if (level === 'WARN') {
      console.warn(logMessage, meta || '');
    } else {
      console.log(logMessage, meta || '');
    }
  }

  static info(workerId: string, message: string, meta?: any) {
    this.log(workerId, 'INFO', message, meta);
  }

  static warn(workerId: string, message: string, meta?: any) {
    this.log(workerId, 'WARN', message, meta);
  }

  static error(workerId: string, message: string, meta?: any) {
    this.log(workerId, 'ERROR', message, meta);
  }
}

export class BackgroundWorker {
  private processor = new ConversationProcessor();
  private isRunning = false;
  private checkInterval = 2000; // Check every 2 seconds
  private workerId: string;
  private metrics: WorkerMetrics;

  constructor(workerId: string) {
    this.workerId = workerId;
    this.metrics = {
      startTime: new Date(),
      conversationsProcessed: 0,
      conversationsFailed: 0,
      lastActivity: new Date(),
      currentConversationId: null,
      currentConversationStartTime: null,
      errors: []
    };
  }

  async start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    WorkerLogger.info(this.workerId, 'Worker starting', {
      checkInterval: this.checkInterval,
      startTime: this.metrics.startTime.toISOString()
    });
    
    this.processLoop();
  }

  async stop() {
    this.isRunning = false;

    // Release any conversation this worker might be processing
    if (this.metrics.currentConversationId) {
      await this.releaseConversation(this.metrics.currentConversationId);
    }

    const uptime = Date.now() - this.metrics.startTime.getTime();
    WorkerLogger.info(this.workerId, 'Worker stopping', {
      uptime: `${Math.round(uptime / 1000)}s`,
      conversationsProcessed: this.metrics.conversationsProcessed,
      conversationsFailed: this.metrics.conversationsFailed,
      errorCount: this.metrics.errors.length
    });
  }

  private async processLoop() {
    WorkerLogger.info(this.workerId, 'Process loop started');
    while (this.isRunning) {
      try {
        await this.checkForPendingConversations();
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.recordError('Process loop error', errorMessage);
        WorkerLogger.error(this.workerId, 'Process loop error', { error: errorMessage });
      }

      // Wait before next check
      await this.sleep(this.checkInterval);
    }
    WorkerLogger.info(this.workerId, 'Process loop ended');
  }

  private async checkForPendingConversations() {
    const supabase = supabaseAdmin;
    let conversations: any[] = [];

    // Use a proper atomic work queue pattern with FOR UPDATE SKIP LOCKED
    // This prevents race conditions by locking rows before updating them
    try {
      // First, try the new atomic claiming function
      const { data: pendingConversations, error: selectError } = await supabase
        .rpc('claim_pending_conversation', {
          worker_id: this.workerId
        });

      if (selectError) {
        // If the function doesn't exist, fall back to the old method
        if (selectError.message?.includes('function claim_pending_conversation') ||
            selectError.code === '42883') {
          WorkerLogger.warn(this.workerId, 'Using fallback conversation claiming method - consider running migration');
          return this.checkForPendingConversationsFallback();
        }

        const errorMessage = selectError instanceof Error ? selectError.message : 'Unknown error';
        this.recordError('Database claim error', errorMessage);
        WorkerLogger.error(this.workerId, 'Error claiming pending conversations', {
          error: errorMessage,
          code: selectError.code,
          details: selectError.details,
          hint: selectError.hint
        });
        return;
      }

      if (!pendingConversations || pendingConversations.length === 0) {
        return; // No pending conversations
      }

      WorkerLogger.info(this.workerId, `Found ${pendingConversations.length} pending conversations to process`);
      conversations = pendingConversations;

      // Validate that conversations is an array
      if (!Array.isArray(conversations)) {
        WorkerLogger.error(this.workerId, 'Invalid conversations data received from claim function', {
          type: typeof conversations,
          data: conversations
        });
        return;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.recordError('Database claim error', errorMessage);
      WorkerLogger.error(this.workerId, 'Error in checkForPendingConversations', { error: errorMessage });
      return;
    }

    this.metrics.lastActivity = new Date();
    WorkerLogger.info(this.workerId, `Claimed ${conversations.length} conversations for processing`, {
      conversationIds: conversations.map(c => c.id),
      claimTime: new Date().toISOString()
    });

    // Process each conversation
    for (const conversation of conversations) {
      if (!this.isRunning) break; // Stop if worker was stopped
      
      // Skip processing if conversation has been cancelled
      if (cancellationManager.isCancelled(conversation.id)) {
        WorkerLogger.info(this.workerId, `Skipping cancelled conversation: ${conversation.id}`);
        
        // Mark as cancelled/failed
        await supabase
          .from('conversations')
          .update({ 
            processing_status: 'failed',
            processing_error: 'Processing was cancelled'
          })
          .eq('id', conversation.id);
        
        // Clean up cancellation tracking
        cancellationManager.cleanup(conversation.id);
        continue;
      }
      
      try {
        this.metrics.currentConversationId = conversation.id;
        this.metrics.currentConversationStartTime = new Date();
        
        WorkerLogger.info(this.workerId, `Starting processing conversation: ${conversation.id}`, {
          userId: conversation.user_id,
          youtubeVideoId: conversation.youtube_video_id
        });
        
        if (conversation.youtube_video_id) {
          const youtubeUrl = `https://www.youtube.com/watch?v=${conversation.youtube_video_id}`;
          // Use "anonymous" as userId for anonymous conversations
          const userId = conversation.user_id || "anonymous";
          await this.processor.processConversation(
            conversation.id,
            youtubeUrl,
            userId
          );
          
          // Success metrics
          this.metrics.conversationsProcessed++;
          const processingTime = this.metrics.currentConversationStartTime ? 
            Date.now() - this.metrics.currentConversationStartTime.getTime() : 0;
            
          WorkerLogger.info(this.workerId, `Successfully processed conversation: ${conversation.id}`, {
            processingTime: `${Math.round(processingTime / 1000)}s`,
            totalProcessed: this.metrics.conversationsProcessed
          });
        } else {
          // Mark as failed if no youtube_video_id
          this.metrics.conversationsFailed++;
          this.recordError('Missing YouTube video ID', 'No YouTube video ID found', conversation.id);
          
          WorkerLogger.error(this.workerId, `No YouTube video ID for conversation: ${conversation.id}`);
          
          await supabase
            .from('conversations')
            .update({ 
              processing_status: 'failed',
              processing_error: 'No YouTube video ID found'
            })
            .eq('id', conversation.id);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Processing failed';
        this.metrics.conversationsFailed++;
        this.recordError('Processing error', errorMessage, conversation.id);
        
        const processingTime = this.metrics.currentConversationStartTime ? 
          Date.now() - this.metrics.currentConversationStartTime.getTime() : 0;
          
        WorkerLogger.error(this.workerId, `Processing error for conversation: ${conversation.id}`, {
          error: errorMessage,
          processingTime: `${Math.round(processingTime / 1000)}s`,
          totalFailed: this.metrics.conversationsFailed
        });
        
        // Check if the error was due to cancellation
        let finalErrorMessage = errorMessage;
        if (errorMessage.includes('Processing cancelled')) {
          finalErrorMessage = 'Processing was cancelled';
        }
        
        // Mark as failed
        await supabase
          .from('conversations')
          .update({ 
            processing_status: 'failed',
            processing_error: finalErrorMessage
          })
          .eq('id', conversation.id);
        
        // Clean up cancellation tracking
        cancellationManager.cleanup(conversation.id);
      } finally {
        // Clear current conversation tracking
        this.metrics.currentConversationId = null;
        this.metrics.currentConversationStartTime = null;
        this.metrics.lastActivity = new Date();
      }
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private recordError(type: string, message: string, conversationId?: string) {
    this.metrics.errors.push({
      timestamp: new Date(),
      error: `${type}: ${message}`,
      conversationId
    });
    
    // Keep only last 50 errors to prevent memory issues
    if (this.metrics.errors.length > 50) {
      this.metrics.errors = this.metrics.errors.slice(-50);
    }
  }

  getMetrics(): WorkerMetrics {
    return { ...this.metrics };
  }

  getStatus() {
    const uptime = Date.now() - this.metrics.startTime.getTime();
    const timeSinceLastActivity = Date.now() - this.metrics.lastActivity.getTime();

    return {
      workerId: this.workerId,
      isRunning: this.isRunning,
      uptime: Math.round(uptime / 1000),
      timeSinceLastActivity: Math.round(timeSinceLastActivity / 1000),
      conversationsProcessed: this.metrics.conversationsProcessed,
      conversationsFailed: this.metrics.conversationsFailed,
      errorCount: this.metrics.errors.length,
      currentConversationId: this.metrics.currentConversationId,
      currentConversationDuration: this.metrics.currentConversationStartTime ?
        Math.round((Date.now() - this.metrics.currentConversationStartTime.getTime()) / 1000) : null
    };
  }

  /**
   * Fallback method for claiming conversations when the atomic function is not available
   * This is the old method that has race conditions but provides backwards compatibility
   */
  private async checkForPendingConversationsFallback() {
    const supabase = supabaseAdmin;

    // Old method - has race conditions but works as fallback
    const { data: conversations, error } = await supabase
      .from('conversations')
      .update({
        processing_status: 'processing',
        processing_started_at: new Date().toISOString(),
        worker_id: this.workerId
      })
      .eq('processing_status', 'pending')
      .select('id, user_id, youtube_video_id')
      .order('created_at', { ascending: true })
      .limit(1);

    if (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.recordError('Database claim error (fallback)', errorMessage);
      WorkerLogger.error(this.workerId, 'Error claiming pending conversations (fallback)', { error: errorMessage });
      return;
    }

    if (!conversations || conversations.length === 0) {
      return; // No pending conversations
    }

    // Continue with the same processing logic as the main method
    this.metrics.lastActivity = new Date();
    WorkerLogger.info(this.workerId, `Claimed ${conversations.length} conversations for processing (fallback)`, {
      conversationIds: conversations.map(c => c.id),
      claimTime: new Date().toISOString()
    });

    // Process each conversation (same logic as main method)
    for (const conversation of conversations) {
      if (!this.isRunning) break;

      if (cancellationManager.isCancelled(conversation.id)) {
        WorkerLogger.info(this.workerId, `Skipping cancelled conversation: ${conversation.id}`);

        await supabase
          .from('conversations')
          .update({
            processing_status: 'failed',
            processing_error: 'Processing was cancelled'
          })
          .eq('id', conversation.id);

        cancellationManager.cleanup(conversation.id);
        continue;
      }

      try {
        this.metrics.currentConversationId = conversation.id;
        this.metrics.currentConversationStartTime = new Date();

        WorkerLogger.info(this.workerId, `Starting processing conversation: ${conversation.id}`, {
          userId: conversation.user_id,
          youtubeVideoId: conversation.youtube_video_id
        });

        if (conversation.youtube_video_id) {
          const youtubeUrl = `https://www.youtube.com/watch?v=${conversation.youtube_video_id}`;
          const userId = conversation.user_id || "anonymous";
          await this.processor.processConversation(
            conversation.id,
            youtubeUrl,
            userId
          );

          this.metrics.conversationsProcessed++;
          const processingTime = this.metrics.currentConversationStartTime ?
            Date.now() - this.metrics.currentConversationStartTime.getTime() : 0;

          WorkerLogger.info(this.workerId, `Successfully processed conversation: ${conversation.id}`, {
            processingTime: `${Math.round(processingTime / 1000)}s`,
            totalProcessed: this.metrics.conversationsProcessed
          });
        } else {
          WorkerLogger.warn(this.workerId, `Conversation ${conversation.id} has no YouTube video ID`);

          await supabase
            .from('conversations')
            .update({
              processing_status: 'failed',
              processing_error: 'No YouTube video ID provided'
            })
            .eq('id', conversation.id);

          this.metrics.conversationsFailed++;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.recordError('Processing error', errorMessage, conversation.id);
        this.metrics.conversationsFailed++;

        WorkerLogger.error(this.workerId, `Failed to process conversation: ${conversation.id}`, {
          error: errorMessage,
          totalFailed: this.metrics.conversationsFailed
        });

        try {
          await supabase
            .from('conversations')
            .update({
              processing_status: 'failed',
              processing_error: errorMessage
            })
            .eq('id', conversation.id);
        } catch (updateError) {
          WorkerLogger.error(this.workerId, `Failed to update conversation status: ${conversation.id}`, {
            error: updateError instanceof Error ? updateError.message : 'Unknown error'
          });
        }

        cancellationManager.cleanup(conversation.id);
      } finally {
        this.metrics.currentConversationId = null;
        this.metrics.currentConversationStartTime = null;
        this.metrics.lastActivity = new Date();
      }
    }
  }

  /**
   * Release a conversation back to pending state
   * This is called when a worker shuts down or encounters an error
   */
  private async releaseConversation(conversationId: string) {
    const supabase = supabaseAdmin;

    try {
      // Try the new atomic release function first
      const { data, error } = await supabase
        .rpc('release_conversation', {
          conversation_id: conversationId,
          worker_id: this.workerId
        });

      if (error) {
        // If the function doesn't exist, fall back to direct update
        if (error.message?.includes('function release_conversation') ||
            error.code === '42883') {
          WorkerLogger.warn(this.workerId, 'Using fallback conversation release method');

          await supabase
            .from('conversations')
            .update({
              processing_status: 'pending',
              processing_started_at: null,
              worker_id: null
            })
            .eq('id', conversationId)
            .eq('processing_status', 'processing');
        } else {
          throw error;
        }
      }

      WorkerLogger.info(this.workerId, `Released conversation: ${conversationId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      WorkerLogger.error(this.workerId, `Failed to release conversation: ${conversationId}`, {
        error: errorMessage
      });
    }
  }
}

export class WorkerPool {
  private workers: BackgroundWorker[] = [];
  private isRunning = false;
  private poolSize: number;
  private startTime: Date | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private metricsLogInterval: NodeJS.Timeout | null = null;

  constructor(poolSize: number = 10) {
    this.poolSize = poolSize;
    
    // Create workers
    for (let i = 0; i < poolSize; i++) {
      const workerId = `worker-${i + 1}`;
      this.workers.push(new BackgroundWorker(workerId));
    }
    
    console.log(`[WorkerPool] Created pool with ${poolSize} workers`);
  }

  async start() {
    if (this.isRunning) {
      console.warn('[WorkerPool] Attempted to start already running pool');
      return;
    }
    
    this.isRunning = true;
    this.startTime = new Date();
    console.log(`[${new Date().toISOString()}] [WorkerPool] Starting pool with ${this.poolSize} workers`);
    
    try {
      // Start all workers
      const startPromises = this.workers.map(worker => worker.start());
      await Promise.all(startPromises);
      
      // Start health monitoring
      this.startHealthMonitoring();
      this.startMetricsLogging();
      
      console.log(`[${new Date().toISOString()}] [WorkerPool] All ${this.poolSize} workers started successfully`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] [WorkerPool] Error starting workers:`, error);
      this.stop(); // Clean up on failure
      throw error;
    }
  }

  async stop() {
    if (!this.isRunning) {
      console.warn('[WorkerPool] Attempted to stop already stopped pool');
      return;
    }

    this.isRunning = false;

    // Stop monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    if (this.metricsLogInterval) {
      clearInterval(this.metricsLogInterval);
      this.metricsLogInterval = null;
    }

    const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;
    const poolStats = this.getPoolStats();

    console.log(`[${new Date().toISOString()}] [WorkerPool] Stopping pool`, {
      uptime: `${Math.round(uptime / 1000)}s`,
      ...poolStats
    });

    // Stop all workers and wait for them to finish gracefully
    const stopPromises = this.workers.map(worker => worker.stop());
    await Promise.all(stopPromises);

    console.log(`[${new Date().toISOString()}] [WorkerPool] All ${this.poolSize} workers stopped`);
  }

  getWorkerCount(): number {
    return this.workers.length;
  }

  isPoolRunning(): boolean {
    return this.isRunning;
  }

  private startHealthMonitoring() {
    // Health check every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000);
  }

  private startMetricsLogging() {
    // Log metrics every 5 minutes
    this.metricsLogInterval = setInterval(() => {
      this.logPoolMetrics();
    }, 5 * 60 * 1000);
  }

  private performHealthCheck() {
    const poolStats = this.getPoolStats();
    const stuckWorkers = this.workers.filter(worker => {
      const status = worker.getStatus();
      // Consider a worker stuck if it's been processing the same conversation for more than 10 minutes
      return status.currentConversationId && status.currentConversationDuration && status.currentConversationDuration > 600;
    });

    if (stuckWorkers.length > 0) {
      console.warn(`[${new Date().toISOString()}] [WorkerPool] Health Check: Found ${stuckWorkers.length} potentially stuck workers`, {
        stuckWorkers: stuckWorkers.map(w => ({
          workerId: w.getStatus().workerId,
          conversationId: w.getStatus().currentConversationId,
          duration: w.getStatus().currentConversationDuration
        }))
      });
    }

    // Log health summary
    console.log(`[${new Date().toISOString()}] [WorkerPool] Health Check:`, {
      activeWorkers: poolStats.activeWorkers,
      idleWorkers: poolStats.idleWorkers,
      totalProcessed: poolStats.totalConversationsProcessed,
      totalFailed: poolStats.totalConversationsFailed,
      avgUptime: `${Math.round(poolStats.averageUptime)}s`
    });
  }

  private logPoolMetrics() {
    const poolStats = this.getPoolStats();
    const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;

    console.log(`[${new Date().toISOString()}] [WorkerPool] Metrics Report:`, {
      poolUptime: `${Math.round(uptime / 1000)}s`,
      ...poolStats,
      recentErrors: this.getRecentErrors()
    });
  }

  private getRecentErrors(): Array<{ workerId: string; error: string; timestamp: string }> {
    const recentErrors: Array<{ workerId: string; error: string; timestamp: string }> = [];
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

    this.workers.forEach(worker => {
      const metrics = worker.getMetrics();
      metrics.errors.forEach(error => {
        if (error.timestamp.getTime() > fiveMinutesAgo) {
          recentErrors.push({
            workerId: worker.getStatus().workerId,
            error: error.error,
            timestamp: error.timestamp.toISOString()
          });
        }
      });
    });

    return recentErrors.slice(-10); // Return last 10 errors
  }

  getPoolStats() {
    const stats = {
      totalWorkers: this.workers.length,
      activeWorkers: 0,
      idleWorkers: 0,
      totalConversationsProcessed: 0,
      totalConversationsFailed: 0,
      totalErrors: 0,
      averageUptime: 0,
      workersStatus: [] as Array<ReturnType<BackgroundWorker['getStatus']>>
    };

    let totalUptime = 0;

    this.workers.forEach(worker => {
      const status = worker.getStatus();
      const metrics = worker.getMetrics();
      
      stats.workersStatus.push(status);
      stats.totalConversationsProcessed += status.conversationsProcessed;
      stats.totalConversationsFailed += status.conversationsFailed;
      stats.totalErrors += status.errorCount;
      totalUptime += status.uptime;
      
      if (status.currentConversationId) {
        stats.activeWorkers++;
      } else {
        stats.idleWorkers++;
      }
    });

    stats.averageUptime = this.workers.length > 0 ? totalUptime / this.workers.length : 0;

    return stats;
  }

  getDetailedStatus() {
    const poolStats = this.getPoolStats();
    const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;

    return {
      isRunning: this.isRunning,
      poolSize: this.poolSize,
      uptime: Math.round(uptime / 1000),
      startTime: this.startTime?.toISOString(),
      ...poolStats
    };
  }

  /**
   * Clean up stale conversations that may have been left in processing state
   * This is useful for recovering from worker crashes or unexpected shutdowns
   */
  async cleanupStaleConversations(timeoutMinutes: number = 30): Promise<number> {
    try {
      const { data, error } = await supabaseAdmin
        .rpc('cleanup_stale_conversations', {
          timeout_minutes: timeoutMinutes
        });

      if (error) {
        // If the function doesn't exist, fall back to direct update
        if (error.message?.includes('function cleanup_stale_conversations') ||
            error.code === '42883') {
          console.warn('[WorkerPool] Using fallback stale conversation cleanup method');

          const { data: cleanupData, error: cleanupError } = await supabaseAdmin
            .from('conversations')
            .update({
              processing_status: 'pending',
              processing_started_at: null,
              worker_id: null,
              processing_error: 'Processing timed out - worker may have crashed'
            })
            .lt('processing_started_at', new Date(Date.now() - timeoutMinutes * 60 * 1000).toISOString())
            .eq('processing_status', 'processing')
            .select('id');

          if (cleanupError) {
            throw cleanupError;
          }

          const cleanedCount = cleanupData?.length || 0;
          console.log(`[WorkerPool] Cleaned up ${cleanedCount} stale conversations (fallback)`);
          return cleanedCount;
        } else {
          throw error;
        }
      }

      const cleanedCount = data || 0;
      console.log(`[WorkerPool] Cleaned up ${cleanedCount} stale conversations`);
      return cleanedCount;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[WorkerPool] Failed to cleanup stale conversations:', errorMessage);
      return 0;
    }
  }
}

// Singleton instance
let workerPoolInstance: WorkerPool | null = null;

export function getWorkerPoolInstance(): WorkerPool {
  if (!workerPoolInstance) {
    const instanceId = Math.random().toString(36).substring(2, 8);
    const processId = process.pid;
    console.log(`[WorkerPool:PID-${processId}] Creating new singleton instance: ${instanceId}`);
    workerPoolInstance = new WorkerPool(10);
  }
  return workerPoolInstance;
}

export function startBackgroundWorker() {
  const processId = process.pid;
  console.log(`[WorkerPool:PID-${processId}] startBackgroundWorker called`);
  const pool = getWorkerPoolInstance();
  pool.start();
  return pool;
}

export async function stopBackgroundWorker() {
  if (workerPoolInstance) {
    await workerPoolInstance.stop();
  }
}

export async function cleanupStaleConversations(timeoutMinutes: number = 30): Promise<number> {
  const pool = getWorkerPoolInstance();
  return pool.cleanupStaleConversations(timeoutMinutes);
}

// Legacy compatibility
export function getWorkerInstance(): WorkerPool {
  return getWorkerPoolInstance();
} 