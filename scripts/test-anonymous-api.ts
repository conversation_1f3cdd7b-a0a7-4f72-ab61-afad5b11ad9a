#!/usr/bin/env tsx

/**
 * Test script to verify anonymous user API endpoints work correctly
 */

import { v4 as uuidv4 } from 'uuid';

const BASE_URL = 'http://localhost:3002';
const TEST_SESSION_ID = uuidv4(); // Use a plain UUID to fit in VARCHAR(36)

async function testAnonymousConversationCreation() {
  console.log('🧪 Testing anonymous conversation creation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/product/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': TEST_SESSION_ID
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('   ✅ Conversation created successfully:', result.conversation_id);
      return result.conversation_id;
    } else {
      console.log('   ❌ Failed to create conversation:', result);
      return null;
    }
  } catch (error) {
    console.error('   ❌ Error creating conversation:', error);
    return null;
  }
}

async function testConversationStatus(conversationId: string) {
  console.log('🧪 Testing conversation status endpoint...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/product/conversations/${conversationId}/status`, {
      headers: {
        'x-session-id': TEST_SESSION_ID
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('   ✅ Status endpoint works:', result.status);
      return true;
    } else {
      console.log('   ❌ Status endpoint failed:', result);
      return false;
    }
  } catch (error) {
    console.error('   ❌ Error getting status:', error);
    return false;
  }
}

async function testConversationProgress(conversationId: string) {
  console.log('🧪 Testing conversation progress endpoint...');
  
  try {
    // Test the progress endpoint with a simple fetch (not EventSource)
    const response = await fetch(`${BASE_URL}/api/product/conversations/${conversationId}/progress`, {
      headers: {
        'x-session-id': TEST_SESSION_ID
      }
    });
    
    if (response.ok) {
      console.log('   ✅ Progress endpoint accessible (status 200)');
      return true;
    } else {
      const result = await response.text();
      console.log('   ❌ Progress endpoint failed:', response.status, result);
      return false;
    }
  } catch (error) {
    console.error('   ❌ Error accessing progress endpoint:', error);
    return false;
  }
}

async function testConversationsList() {
  console.log('🧪 Testing conversations list endpoint...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/product/conversations`, {
      headers: {
        'x-session-id': TEST_SESSION_ID
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('   ✅ Conversations list works, found:', result.data?.length || 0, 'conversations');
      return true;
    } else {
      console.log('   ❌ Conversations list failed:', result);
      return false;
    }
  } catch (error) {
    console.error('   ❌ Error getting conversations:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting anonymous API tests...');
  console.log(`   Using session ID: ${TEST_SESSION_ID}`);
  
  try {
    // Test 1: Create conversation
    const conversationId = await testAnonymousConversationCreation();
    if (!conversationId) {
      console.log('❌ Cannot continue tests without a conversation');
      return;
    }
    
    // Test 2: Check status endpoint
    await testConversationStatus(conversationId);
    
    // Test 3: Check progress endpoint
    await testConversationProgress(conversationId);
    
    // Test 4: Check conversations list
    await testConversationsList();
    
    console.log('\n✅ All tests completed!');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

if (require.main === module) {
  main();
}
