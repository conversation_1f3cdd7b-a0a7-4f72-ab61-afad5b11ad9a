import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getAuthContext, validateAccess } from '@/lib/auth/apiAuth';

interface ErrorWithResponse {
  response: {
    data: unknown;
    status: number;
  };
  message: string;
}

const DIFY_API_KEY = process.env.DIFY_API_KEY_SUMMARY;
const DIFY_BASE_URL = 'http://localhost/v1';

// This is a streaming endpoint
export async function POST(request: NextRequest) {
  const requestData = await request.json();

  try {
    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      console.error('Access validation failed:', accessValidation.error);
      return NextResponse.json({ error: accessValidation.error }, { status: 401 });
    }

    // Use user ID for authenticated users, session ID for anonymous users
    const userId = authContext.isAuthenticated && authContext.user
      ? `${authContext.user.id}`
      : `session_${authContext.sessionId}`;

    const userInfo = authContext.isAuthenticated
      ? `user: ${authContext.user?.id}`
      : `session: ${authContext.sessionId}`;
    console.log('Processing stream request for', userInfo);
    
    // Check if this is a follow-up message by looking for existing summary
    let messageType = 'first';
    let resolvedConversationId = requestData.conversation_id;
    
    if (requestData.conversation_id) {
      console.log('Checking for existing summary for conversation:', requestData.conversation_id);
      
      // First, resolve the conversation ID (could be Dify or Supabase ID)
      const { data: conversation, error: conversationError } = await supabase
        .from('conversations')
        .select('id, dify_summary_id')
        .or(`id.eq.${requestData.conversation_id},dify_summary_id.eq.${requestData.conversation_id}`)
        .eq('user_id', user.id)
        .single();

      console.log('Database lookup result:', { conversation, conversationError });

      if (conversation) {
        resolvedConversationId = conversation.dify_summary_id || conversation.id;
        console.log('Found conversation in DB:', {
          supabaseId: conversation.id,
          difySummaryId: conversation.dify_summary_id,
          difySummaryIdType: typeof conversation.dify_summary_id,
          difySummaryIdIsNull: conversation.dify_summary_id === null,
          difySummaryIdIsEmpty: conversation.dify_summary_id === '',
          resolvedId: resolvedConversationId
        });
        
        // Check for existing summary
        const { data: content, error: contentError } = await supabase
          .from('content')
          .select('id')
          .eq('conversation_id', conversation.id)
          .single();

        if (content && !contentError) {
          messageType = 'follow_up';
          console.log('Found existing summary, setting message_type to follow_up');
        } else {
          console.log('No existing summary found, setting message_type to first');
        }
      } else {
        console.log('Conversation not found in database, treating as first message');
        console.log('Using original conversation ID as fallback:', requestData.conversation_id);
        resolvedConversationId = requestData.conversation_id;
      }
    }
    
    console.log('Final message type:', messageType);
    console.log('Final conversation ID for Dify:', resolvedConversationId);
    
    // Build request body - use streaming mode to get node status updates
    const requestBody = {
      query: requestData.text,
      inputs: {
        message_type: messageType
      },
      response_mode: 'streaming', // Important: Use streaming mode to get node status
      user: userId
    };

    // Add conversation_id if it exists to maintain context
    if (resolvedConversationId) {
      Object.assign(requestBody, { conversation_id: resolvedConversationId });
      console.log('Added conversation_id to request:', resolvedConversationId);
    } else {
      console.log('No conversation_id to add to request');
    }
    
    console.log('Dify request body:', JSON.stringify(requestBody, null, 2));
    
    // Create a fetch request to Dify
    console.log('Making fetch request to Dify endpoint:', `${DIFY_BASE_URL}/chat-messages`);
    const difyResponse = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DIFY_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('Dify response status:', difyResponse.status);
    console.log('Dify response headers:', Object.fromEntries(difyResponse.headers.entries()));
    
    if (!difyResponse.ok) {
      const errorText = await difyResponse.text();
      console.error('Dify API error response:', errorText);
      throw new Error(`Dify API error: ${difyResponse.status} - ${errorText}`);
    }
    
    // Create a TransformStream to pipe the response
    const { readable, writable } = new TransformStream();
    
    // Pipe the Dify response through to our client
    if (difyResponse.body) {
      console.log('Piping Dify response body to client');
      difyResponse.body.pipeTo(writable);
    } else {
      console.error('No response body from Dify');
      throw new Error('No response body from Dify');
    }
    
    // Return the stream to the client
    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });

  } catch (error: unknown) {
    console.error('Full error object:', error);
    
    // Type guard for error with response property
    if (error && typeof error === 'object' && 'response' in error) {
      const errorWithResponse = error as ErrorWithResponse;
      console.error('Error response data:', errorWithResponse.response.data);
      console.error('Error response status:', errorWithResponse.response.status);
      const errorMessage = errorWithResponse.response.data || (error instanceof Error ? error.message : 'Unknown error');
      return NextResponse.json({ message: 'Error streaming content:', error: errorMessage }, { status: 500 });
    }
    
    // Default error handling
    return NextResponse.json({ 
      message: 'Error streaming content:', 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
} 