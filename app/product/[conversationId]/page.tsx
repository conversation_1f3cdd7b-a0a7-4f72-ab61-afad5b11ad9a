'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import ChatInterface from '../ChatInterface';
import { useUserData } from '@/contexts/UserDataContext';
import SuccessAlert from '../../components/ui/SuccessAlert';
import LoginPrompt from '../../components/ui/LoginPrompt';
import { Loader, Alert } from '@mantine/core';
import { IconExclamationCircle } from '@tabler/icons-react';
import { getSessionHeaders } from '@/lib/session/sessionManager';

interface ConversationAccess {
	hasAccess: boolean;
	isLoading: boolean;
	error: string | null;
}

export default function ConversationPage() {
	const { user, isLoading: userLoading } = useUserData();
	const params = useParams();
	const conversationId = params.conversationId as string;
	const [conversationAccess, setConversationAccess] = useState<ConversationAccess>({
		hasAccess: false,
		isLoading: true,
		error: null
	});
	const [showNewChatModal, setShowNewChatModal] = useState(false);

	// Check conversation access when component mounts or user changes
	useEffect(() => {
		const checkConversationAccess = async () => {
			if (!conversationId || userLoading) return;

			setConversationAccess({ hasAccess: false, isLoading: true, error: null });

			try {
				const response = await fetch(`/api/product/conversations/${conversationId}/fetch`, {
					headers: {
						...getSessionHeaders()
					}
				});

				if (response.ok) {
					setConversationAccess({ hasAccess: true, isLoading: false, error: null });
				} else if (response.status === 403) {
					// Access denied
					setConversationAccess({
						hasAccess: false,
						isLoading: false,
						error: 'You don\'t have access to this conversation. Starting a new chat instead.'
					});
					setShowNewChatModal(true);
				} else if (response.status === 404) {
					// Conversation not found
					setConversationAccess({
						hasAccess: false,
						isLoading: false,
						error: 'This conversation was not found. Starting a new chat instead.'
					});
					setShowNewChatModal(true);
				} else {
					// Other error
					const errorData = await response.json().catch(() => ({}));
					setConversationAccess({
						hasAccess: false,
						isLoading: false,
						error: errorData.error || 'Failed to load conversation. Starting a new chat instead.'
					});
					setShowNewChatModal(true);
				}
			} catch (error) {
				console.error('Error checking conversation access:', error);
				setConversationAccess({
					hasAccess: false,
					isLoading: false,
					error: 'Failed to load conversation. Starting a new chat instead.'
				});
				setShowNewChatModal(true);
			}
		};

		checkConversationAccess();
	}, [conversationId, userLoading]);

	// Show loading state
	if (userLoading || conversationAccess.isLoading) {
		return <div className="fixed top-0 left-0 w-full h-full flex justify-center items-center">
			<Loader />
		</div>;
	}

	return (
		<>
			<SuccessAlert />
			{!user && <LoginPrompt />}

			{/* Access Denied Alert */}
			{conversationAccess.error && (
				<Alert
					icon={<IconExclamationCircle size={16} />}
					title="Access Issue"
					color="red"
					onClose={() => setConversationAccess(prev => ({ ...prev, error: null }))}
					withCloseButton
					style={{
						position: 'fixed',
						top: '20px',
						left: '50%',
						transform: 'translateX(-50%)',
						zIndex: 1000
					}}
				>
					{conversationAccess.error}
				</Alert>
			)}

			<ChatInterface
				initialConversationId={conversationAccess.hasAccess ? conversationId : null}
				user={user}
				forceShowNewChat={showNewChatModal}
				conversationAccessDenied={!conversationAccess.hasAccess && !!conversationId}
			/>
		</>
	);
}